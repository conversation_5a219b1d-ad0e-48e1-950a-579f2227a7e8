---
type: "agent_requested"
description: "Example description"
---
Always respond in 中文

PowerShell不支持&&语法

# 🧠 Unity C# Coding Style Guide (Bilingual: English → 中文)

## 1. Naming Conventions

Use PascalCase for class names, structs, enums, public fields, properties, and methods.  
Use camelCase for private fields, prefixed with `m_` (e.g., `m_health`).  
Prefix static variables with `s_`, constants with `k_`, and interfaces with `I` (e.g., `IDamageable`).  
Enum names should be singular and use PascalCase for members.  
Method parameters use `camelCase` (no underscores).  

使用 PascalCase（大驼峰）命名类、结构体、枚举、公共字段、属性和方法。  
私有字段使用 camelCase（小驼峰）并加上 `m_` 前缀（如 `m_health`）。  
静态变量用 `s_` 前缀，常量用 `k_` 前缀，接口名称用 `I` 开头（如 `IDamageable`）。  
枚举命名使用单数，成员也使用 PascalCase。  
方法参数使用 camelCase，且不加下划线。

---

## 2. Comments & Documentation

Use `//` for inline comments only when the code isn’t self-explanatory.  
Use `[Tooltip]` for serialized fields instead of comments.  
Avoid `// Created by` comments—use version control instead.  
Avoid `#region` as it hides complexity.  
Use `/// <summary>` for public method documentation and Intellisense support.  

只在代码逻辑不明显时使用 `//` 注释。  
如需解释序列化字段，优先使用 `[Tooltip]` 而非注释。  
避免使用作者信息注释，如 `// Created by`，用版本控制系统追踪变更。  
不推荐使用 `#region`，容易掩盖类的复杂度。  
公共方法应使用 `/// <summary>` 注释以支持智能提示（Intellisense）。

---

## 3. Formatting & Structure

Use K&R brace style (`{` on the same line).  
Always use curly braces `{}` for control structures, even for one-liners.  
Declare one variable per line.  
Member order: Fields, Properties, Events, Constructors, Methods, Inner classes.  
Each MonoBehaviour script should only contain one class with the same name as the file.  

使用 K&R 风格的大括号（`{` 与语句同行）。  
所有控制结构（如 `if`, `for`）即使只有一行也必须使用 `{}` 包裹。  
每行仅声明一个变量，保持清晰。  
类内结构顺序：字段、属性、事件、构造函数、方法、内部类。  
每个 MonoBehaviour 脚本文件只能有一个类，且类名必须与文件名一致。

---

## 4. Whitespace & Spacing

Use one space before conditionals like `if (x == y)`.  
Use space after commas in arguments.  
No space between function name and parentheses: `DoSomething()`  
No spaces inside array brackets: `array[i]`  
Use blank lines between method definitions.  

控制结构如 `if (x == y)` 在条件前空一格。  
函数参数之间逗号后应加空格。  
函数名和括号之间不能有空格，如：`DoSomething()`  
数组下标中不应加空格，如：`array[i]`  
方法之间使用空行分隔逻辑块。

---

## 5. Unity-Specific Practices

Use `[SerializeField]` to expose private fields in the Inspector.  
Use `[Range(min, max)]` for float sliders in Inspector.  
Use `TryGetComponent<T>(out T)` to avoid null exceptions.  
Avoid `GameObject.Find()` and `Transform.Find()`.  
Always use TextMeshPro for UI text.  
Use object pooling for frequently spawned objects.  
Use ScriptableObject for data-driven architecture.  

使用 `[SerializeField]` 在 Inspector 中展示私有字段。  
使用 `[Range(min, max)]` 创建浮点值滑动条限制。  
使用 `TryGetComponent<T>(out T)` 避免空引用异常。  
避免使用 `GameObject.Find()` 与 `Transform.Find()`。  
所有 UI 文本应使用 TextMeshPro。  
频繁生成的对象应使用对象池技术。  
数据驱动逻辑应采用 ScriptableObject。

---

## 6. Method Naming & Parameters

Start method names with verbs like `Initialize()`, `LoadData()`.  
Boolean-returning methods should be phrased as questions: `IsDead()`, `HasItem()`.  
Avoid methods with many parameters; prefer grouping into a struct/class.  

方法命名应以动词开头，如 `Initialize()`、`LoadData()`。  
返回布尔值的方法命名应像疑问句，例如 `IsDead()`、`HasItem()`。  
避免过多参数的方法，建议用结构体或类封装参数。

---

## 7. Miscellaneous

Use `var` when the type is obvious.  
Use `switch` for long if-else chains.  
Use `[ContextMenu]` for in-editor utilities.  
Wrap editor-only code with `#if UNITY_EDITOR`.  

当类型明显时可使用 `var`。  
使用 `switch` 替代冗长的 `if-else` 链提高可读性。  
编辑器调试功能可使用 `[ContextMenu]` 注解。  
编辑器专用代码必须包裹在 `#if UNITY_EDITOR` 中。
