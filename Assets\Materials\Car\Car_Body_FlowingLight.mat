%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Car_Body_FlowingLight
  m_Shader: {fileID: 4800000, guid: e260cfa7296ee7642b167f1eb5be5023, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AlphaTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorRampTex:
        m_Texture: {fileID: 2800000, guid: 359431ef2af87794198688f7c632c1e3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorRampTexGradient:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorSwapTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortTex:
        m_Texture: {fileID: 2800000, guid: 7aad8c583ef292e48b06af0d1f2fab97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadeBurnTex:
        m_Texture: {fileID: 2800000, guid: 677cca399782dea41aedc1d292ecb67d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadeTex:
        m_Texture: {fileID: 2800000, guid: 7aad8c583ef292e48b06af0d1f2fab97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GlowTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NewTex_1:
        m_Texture: {fileID: 2800000, guid: de09ebd1990d8cd4c94b9c93886a171a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineDistortTex:
        m_Texture: {fileID: 2800000, guid: 7aad8c583ef292e48b06af0d1f2fab97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineTex:
        m_Texture: {fileID: 2800000, guid: 74087f6d03f233e4a8a142fa01f9e5cf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OverlayTex:
        m_Texture: {fileID: 2800000, guid: 1c568bfd1d1e4aa4fa698e53a1f74872, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: -0.5, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShineMask:
        m_Texture: {fileID: 2800000, guid: dbe687f90870a6445b15d31923af572b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - AnimatedMouvementUV_Speed_1: 0.239
    - AnimatedMouvementUV_X_1: 1
    - AnimatedMouvementUV_Y_1: 1
    - AnimatedOffsetUV_Speed_1: 0.1
    - AnimatedOffsetUV_X_1: 0.211
    - AnimatedOffsetUV_Y_1: 0.211
    - AnimatedOffsetUV_ZoomX_1: 2.205
    - AnimatedOffsetUV_ZoomY_1: 2.591
    - Burn_Pixel_Size: 59.1
    - Burn_Seed: 0.555
    - Burn_Value: 0.384
    - Displacement_Value: 0.3
    - DistanceX: 0.3
    - DistanceY: 0.3
    - DistortionUV_DistanceX_1: 0.3
    - DistortionUV_DistanceY_1: 0.3
    - DistortionUV_Speed_1: 1
    - DistortionUV_WaveX_1: 10
    - DistortionUV_WaveY_1: 10
    - Effect_Fade: 0.277
    - Fire_Addition: 2.08
    - Fire_Displacement_Value: -0.019
    - Fire_Pixel_Size: 128
    - Intensity: 1
    - KaleidoscopeUV_Number_1: 1
    - KaleidoscopeUV_PosX_1: 0.5
    - KaleidoscopeUV_PosY_1: 0.5
    - OffsetUV_X_1: 0.007
    - OffsetUV_Y_1: 0
    - OffsetUV_ZoomX_1: 1
    - OffsetUV_ZoomY_1: 1
    - PixelSnap: 0
    - Pixel_Size: 22
    - PosX: 0
    - PosY: 0
    - Speed: 1
    - TurnLiquid_Value: 1
    - TurnToInfinite_Value: 1
    - WaveX: 2
    - WaveY: 2
    - Zoom: 1
    - _Add_Fade_1: 1
    - _Alpha: 1
    - _AlphaClip: 0
    - _AlphaCutoffValue: 0.25
    - _AlphaIntensity_Fade_1: 2.79
    - _AlphaOutlineBlend: 1
    - _AlphaOutlineGlow: 5
    - _AlphaOutlineMinAlpha: 0
    - _AlphaOutlinePower: 1
    - _AlphaRoundThreshold: 0.5
    - _AlphaToMask: 0
    - _AnimatedBurn_Fade: 0.149
    - _AnimatedBurn_Offset: 1
    - _AnimatedBurn_Speed: -2
    - _BillboardY: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BlurHD: 0
    - _BlurIntensity: 10
    - _Brightness: 0
    - _BumpScale: 1
    - _ChromAberrAlpha: 0.4
    - _ChromAberrAmount: 1
    - _CircleFade_Dist_1: 1
    - _CircleFade_PosX_1: -0.54
    - _CircleFade_PosY_1: 0.5
    - _CircleFade_Size_1: 0.139
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _ClipUvDown: 0
    - _ClipUvLeft: 0
    - _ClipUvRight: 0
    - _ClipUvUp: 0
    - _ColorChangeLuminosity: 0
    - _ColorChangeTolerance: 0.25
    - _ColorChangeTolerance2: 0.25
    - _ColorChangeTolerance3: 0.25
    - _ColorMask: 15
    - _ColorRampBlend: 0.785
    - _ColorRampLuminosity: 0.1
    - _ColorRampOutline: 0
    - _ColorSwapBlend: 1
    - _ColorSwapBlueLuminosity: 0.5
    - _ColorSwapGreenLuminosity: 0.5
    - _ColorSwapRedLuminosity: 0.5
    - _Contrast: 1.64
    - _Cull: 2
    - _CullingOption: 0
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DistortAmount: 0.5
    - _DistortTexXSpeed: 5
    - _DistortTexYSpeed: 5
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EditorDrawers: 6
    - _Effect_Fade: 1
    - _Emboss_Angle_1: -0.051
    - _Emboss_Distance_1: 2.16
    - _Emboss_Intensity_1: 1.479
    - _Emboss_grayfade_1: 1
    - _Emboss_original_1: 1
    - _EnableExternalAlpha: 0
    - _EnvironmentReflections: 1
    - _FadeAmount: -0.1
    - _FadeBurnGlow: 2
    - _FadeBurnTransition: 0.075
    - _FadeBurnWidth: 0.025
    - _FishEyeUvAmount: 0.35
    - _FlickerAlpha: 0.432
    - _FlickerFreq: 4.82
    - _FlickerPercent: 0.334
    - _GhostBlend: 1
    - _GhostColorBoost: 1
    - _GhostTransparency: 0
    - _GlitchAmount: 20
    - _GlitchSize: 5
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Glow: 10
    - _GlowGlobal: 1
    - _GradBlend: 0.289
    - _GradBoostX: 1.42
    - _GradBoostY: 1.2
    - _GradIsRadial: 1
    - _GrassManualAnim: 1
    - _GrassManualToggle: 0
    - _GrassRadialBend: 0.1
    - _GrassSpeed: 2
    - _GrassWind: 20
    - _GreyscaleBlend: 1
    - _GreyscaleLuminosity: 0
    - _GreyscaleOutline: 0
    - _HandDrawnAmount: 10
    - _HandDrawnSpeed: 5
    - _HitEffectBlend: 0.147
    - _HitEffectGlow: 1
    - _HologramBlend: 1
    - _HologramMaxAlpha: 0.75
    - _HologramMinAlpha: 0.1
    - _HologramStripesAmount: 0.1
    - _HologramStripesSpeed: 4.5
    - _HologramUnmodAmount: 0
    - _HsvBright: 1
    - _HsvSaturation: 1
    - _HsvShift: 180
    - _InnerOutlineAlpha: 1
    - _InnerOutlineGlow: 4
    - _InnerOutlineThickness: 1
    - _LerpUV_Fade_1: 1
    - _Lerp_Fade_1: 0
    - _MaxXUV: 1
    - _MaxYUV: 1
    - _Metallic: 0
    - _MinXUV: 0
    - _MinYUV: 0
    - _Mode: 2
    - _MotionBlurAngle: 0.1
    - _MotionBlurDist: 1.25
    - _MyDstMode: 10
    - _MySrcMode: 5
    - _NegativeAmount: 1
    - _OcclusionStrength: 1
    - _OffsetUvX: 0
    - _OffsetUvY: 0
    - _OnlyInnerOutline: 0
    - _OnlyOutline: 0
    - _OutlineAlpha: 1
    - _OutlineDistortAmount: 0.5
    - _OutlineDistortTexXSpeed: 5
    - _OutlineDistortTexYSpeed: 5
    - _OutlineGlow: 1.5
    - _OutlinePixelWidth: 1
    - _OutlineTexXSpeed: 10
    - _OutlineTexYSpeed: 0
    - _OutlineWidth: 0.004
    - _OverlayBlend: 0.427
    - _OverlayGlow: 11.4
    - _OverlayTextureScrollXSpeed: 1.43
    - _OverlayTextureScrollYSpeed: 0
    - _Parallax: 0.02
    - _PinchUvAmount: 0.35
    - _PixelateSize: 32
    - _PosterizeGamma: 0.75
    - _PosterizeNumColors: 8
    - _PosterizeOutline: 0
    - _QueueOffset: 0
    - _RadialClip: 45
    - _RadialClip2: 0
    - _RadialStartAngle: 90
    - _RandomSeed: 0
    - _ReceiveShadows: 1
    - _RectSize: 1
    - _RotateUvAmount: 0
    - _RoundWaveSpeed: 2
    - _RoundWaveStrength: 0.7
    - _ShadowAlpha: 0.5
    - _ShadowLight_Intensity_1: 4
    - _ShadowLight_NoSprite_1: 1
    - _ShadowLight_PosX_1: -0.341
    - _ShadowLight_PosY_1: 0.044
    - _ShadowLight_Precision_1: 15.3
    - _ShadowLight_Size_1: 0
    - _ShadowX: 0.1
    - _ShadowY: -0.05
    - _ShakeUvSpeed: 2.5
    - _ShakeUvX: 1.5
    - _ShakeUvY: 1
    - _ShineGlow: 15.5
    - _ShineLocation: 0.493
    - _ShineRotate: 0
    - _ShineWidth: 0.05
    - _Shininess: 0.078125
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SpriteFade: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    - _Surface: 0
    - _TextureScrollXSpeed: 1
    - _TextureScrollYSpeed: 0
    - _TurnGold_Speed_1: 8
    - _TurnTransparent_Speed_1: 1
    - _TwistUvAmount: 1
    - _TwistUvPosX: 0.5
    - _TwistUvPosY: 0.5
    - _TwistUvRadius: 0.75
    - _UVSec: 0
    - _WarpScale: 0.5
    - _WarpSpeed: 8
    - _WarpStrength: 0.025
    - _WaveAmount: 7
    - _WaveSpeed: 10
    - _WaveStrength: 7.5
    - _WaveX: 0
    - _WaveY: 0.5
    - _WorkflowMode: 1
    - _ZTestMode: 4
    - _ZWrite: 1
    - _ZoomUvAmount: 0.5
    m_Colors:
    - _AlphaOutlineColor: {r: 1, g: 1, b: 1, a: 1}
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorChangeNewCol: {r: 0.0056602955, g: 0.3041741, b: 1, a: 1}
    - _ColorChangeNewCol2: {r: 1, g: 1, b: 0, a: 1}
    - _ColorChangeNewCol3: {r: 1, g: 1, b: 0, a: 1}
    - _ColorChangeTarget: {r: 0.28235295, g: 0.37647063, b: 0.5764706, a: 1}
    - _ColorChangeTarget2: {r: 1, g: 0, b: 0, a: 1}
    - _ColorChangeTarget3: {r: 1, g: 0, b: 0, a: 1}
    - _ColorSwapBlue: {r: 1, g: 1, b: 1, a: 1}
    - _ColorSwapGreen: {r: 1, g: 1, b: 1, a: 1}
    - _ColorSwapRed: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FadeBurnColor: {r: 1, g: 1, b: 0, a: 1}
    - _FillColor_Color_1: {r: 0.38148788, g: 1, b: 0.19117647, a: 1}
    - _Flip: {r: 1, g: 1, b: 1, a: 1}
    - _GlowColor: {r: 1, g: 1, b: 1, a: 1}
    - _GradBotLeftCol: {r: 0, g: 0, b: 1, a: 1}
    - _GradBotRightCol: {r: 0, g: 1, b: 0, a: 1}
    - _GradTopLeftCol: {r: 1, g: 0, b: 0, a: 1}
    - _GradTopRightCol: {r: 1, g: 1, b: 0, a: 1}
    - _GreyscaleTintColor: {r: 1, g: 1, b: 1, a: 1}
    - _HitEffectColor: {r: 1, g: 1, b: 1, a: 1}
    - _HologramStripeColor: {r: 0, g: 1, b: 1, a: 1}
    - _InnerOutlineColor: {r: 1, g: 0, b: 0, a: 1}
    - _OutlineColor: {r: 1, g: 1, b: 1, a: 1}
    - _OverlayColor: {r: 0.90533745, g: 0.74509805, b: 1, a: 0.14901961}
    - _RendererColor: {r: 1, g: 1, b: 1, a: 1}
    - _ShadowColor: {r: 0, g: 0, b: 0, a: 1}
    - _ShadowLight_Color_1: {r: 0, g: 0.7, b: 1, a: 1}
    - _ShineColor: {r: 0.9210231, g: 0.53396225, b: 1, a: 0.35686275}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _TintRGBA_Color_1: {r: 0.03676468, g: 1, b: 0.32006916, a: 1}
  m_BuildTextureStacks: []
