%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: HologramParasiteTint
  m_Shader: {fileID: 4800000, guid: a757c21eb758ffd4c84dba717c5d1a2d, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - ETC1_EXTERNAL_ALPHA
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NewTex_1:
        m_Texture: {fileID: 2800000, guid: c15a3825223d08f48a238359630d20c8, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NewTex_2:
        m_Texture: {fileID: 2800000, guid: 80d2b68946bdd4349847218c44f708d1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - Break_Seed: 1.141
    - Break_Size: 12.1
    - Break_Value: -0.34324664
    - Burn_Pixel_Size: 61
    - Burn_Seed: 0.482
    - Burn_Value: 0.22600284
    - Compression_Value: 9.285
    - Desintegration_Pixel_Size: 19
    - Desintegration_Speed: 0.5
    - Desintegration_Value: 0
    - Disolve_Seed: 0.5
    - Disolve_Value: 0.57083243
    - Displacement_Value: -0.30740225
    - DistortionUV_DistanceX_1: 0.507
    - DistortionUV_DistanceY_1: 0.598
    - DistortionUV_Speed_1: 0.71
    - DistortionUV_WaveX_1: 14.9
    - DistortionUV_WaveY_1: 19.8
    - DoodleUV_Frame_1: 10.11
    - DoodleUV_Size_1: 7.22
    - Effect_Fade: 0
    - Effect_Fading: 1
    - Explode_Value: 0.35749298
    - FishEyeUV_Size_1: 0.418
    - Glass_Displacement: -0.27332112
    - Glass_Rotation: -0.689
    - Glass_Seed: 1.067
    - Glass_Size: 13.6
    - Parasite_Angle: -0.241
    - Parasite_Distance: -0.247
    - PixelSnap: 0
    - Pixel_Size: 19
    - Seed: 0.6
    - _AutomaticLerp_Speed_1: 0.487
    - _BlurHQPlus_Intensity_1: 32.6
    - _Blur_Intensity_1: -0.2695522
    - _Brightness_Fade_1: 0.2868607
    - _BumpScale: 1
    - _CircleFade_Dist_1: 0.2
    - _CircleFade_PosX_1: 0.559
    - _CircleFade_PosY_1: 0.5
    - _CircleFade_Size_1: 0.54899865
    - _CircleHole_Dist_1: 0.11
    - _CircleHole_PosX_1: 0.5
    - _CircleHole_PosY_1: 0.5
    - _CircleHole_Size_1: 0
    - _ColorFilters_Fade_1: 0.89434236
    - _ColorHSV_Brightness_1: 1.26
    - _ColorHSV_Hue_1: 254.62515
    - _ColorHSV_Saturation_1: 1
    - _ColorMask: 15
    - _Cutoff: 0.5
    - _Destroyer_Speed_1: 0.5
    - _Destroyer_Value_1: 0.32481977
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _Effect_Fade: 1
    - _EmbossFull_Angle_1: 0.4936013
    - _EmbossFull_Distance_1: 3.03
    - _EmbossFull_Intensity_1: 0.46
    - _EmbossFull_grayfade_1: 2
    - _EmbossFull_original_1: 1
    - _Emboss_Angle_1: -0.9955175
    - _Emboss_Distance_1: 3.61
    - _Emboss_Intensity_1: 0.18
    - _Emboss_grayfade_1: 1.28
    - _Emboss_original_1: 1
    - _GhostFX_ClipDown_1: 15.942461
    - _GhostFX_ClipLeft_1: 0.566
    - _GhostFX_ClipRight_1: 0.433
    - _GhostFX_ClipUp_1: 0.705
    - _GhostFX_Smooth_1: 0.488
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _GrayScale_Fade_1: 0.44531453
    - _HdrCreate_Value_1: 0.8300257
    - _InverseColor_Fade_1: 0.9232315
    - _LerpUV_Fade_1: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _OperationBlend_Fade_1: 1
    - _OperationBlend_Fade_2: 1
    - _Parallax: 0.02
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SpriteFade: 1
    - _SrcBlend: 1
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    - _UVSec: 0
    - _ZWrite: 1
    - __CompressionFX_Value_1: 0.5507562
    m_Colors:
    - Tint_Color: {r: 0, g: 0.9117646, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorRGBA_Color_1: {r: 1, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FillColor_Color_1: {r: 0.3814879, g: 1, b: 0.1911765, a: 1}
    - _TintRGBA_Color_1: {r: 1, g: 0.7941177, b: 0, a: 1}
  m_BuildTextureStacks: []
