%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 935899b62f48ae5498594680ed17d133, type: 3}
  m_Name: D1 Database
  m_EditorClassIdentifier: 
  version: 
  author: 
  description: 
  globalUserScript: 
  emphasisSettings:
  - color: {r: 1, g: 1, b: 1, a: 1}
    bold: 0
    italic: 0
    underline: 0
  - color: {r: 1, g: 0, b: 0, a: 1}
    bold: 0
    italic: 0
    underline: 0
  - color: {r: 0, g: 1, b: 0, a: 1}
    bold: 0
    italic: 0
    underline: 0
  - color: {r: 0, g: 0, b: 1, a: 1}
    bold: 0
    italic: 0
    underline: 0
  baseID: 1
  actors:
  - id: 2
    fields:
    - title: Name
      value: Akira
      type: 0
      typeString: 
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: 
    - title: IsPlayer
      value: FALSE
      type: 2
      typeString: CustomFieldType_Boolean
    portrait: {fileID: 0}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 5
    fields:
    - title: Name
      value: "\u9646\u4FEE"
      type: 0
      typeString: 
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: 
    - title: IsPlayer
      value: TRUE
      type: 2
      typeString: CustomFieldType_Boolean
    portrait: {fileID: 0}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 6
    fields:
    - title: Name
      value: 
      type: 0
      typeString: 
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: 
    - title: IsPlayer
      value: FALSE
      type: 2
      typeString: CustomFieldType_Boolean
    portrait: {fileID: 0}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  items: []
  locations: []
  variables: []
  conversations:
  - id: 1
    fields:
    - title: Title
      value: "\u5F00\u573A\u767D"
      type: 0
      typeString: 
    - title: Description
      value: 
      type: 0
      typeString: 
    - title: Actor
      value: 5
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 0
        destinationConversationID: 1
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 419.3407
        y: 55.57361
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Actor
        value: 6
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u2026\u2026\u6ECB\u2026\u2026\u2026\u2026\u6ECB\u2026\u2026</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 1
        destinationConversationID: 1
        destinationDialogueID: 2
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 422.0836
        y: 110.3736
        width: 160
        height: 30
    - id: 2
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u6D4B\u8BD5\uFF0C\u6D4B\u8BD5\u2026\u2026\u8FD9\u91CC\u662F\u300A\u6DF1\u591C\u8FDE\u7EBF\u300B\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 2
        destinationConversationID: 1
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 420.1407
        y: 156.3736
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u58F0\u97F3\u4FE1\u566A\u6BD4\u6B63\u5E38\uFF0C\u7535\u5E73\u5728\u5B89\u5168\u8303\u56F4\u5185\u3002\u53EF\u4EE5\u5F00\u59CB\u4E86\uFF0C\u9646\u4FEE\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 3
        destinationConversationID: 1
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 420.1407
        y: 203.9737
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u6536\u5230\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 4
        destinationConversationID: 1
        destinationDialogueID: 5
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 418.5407
        y: 257.9737
        width: 160
        height: 30
    - id: 5
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u665A\u4E0A\u597D\uFF0C\u6211\u662F\u9646\u4FEE\u3002\u6536\u5230\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 5
        destinationConversationID: 1
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 421.7407
        y: 305.5737
        width: 160
        height: 30
    - id: 6
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u2026\u2026\u4E94\u5E74\u4E86\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 6
        destinationConversationID: 1
        destinationDialogueID: 7
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 420.1407
        y: 357.9737
        width: 160
        height: 30
    - id: 7
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u8FD9\u4E2A\u8BCD\u8BF4\u51FA\u53E3\uFF0C\u820C\u5934\u90FD\u89C9\u5F97\u6709\u70B9\u50F5\u786C\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 7
        destinationConversationID: 1
        destinationDialogueID: 8
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 419.0769
        y: 405.8461
        width: 160
        height: 30
    - id: 8
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u6BD5\u7ADF\u4E94\u5E74\u6CA1\u6B63\u7ECF\u8BF4\u8FC7\u8FD9\u4E48\u957F\u7684\u8BDD\u4E86\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 8
        destinationConversationID: 1
        destinationDialogueID: 9
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 420.1407
        y: 456.9099
        width: 160
        height: 30
    - id: 9
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u2026\u2026\u4E5F\u8BB8\u5427\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 9
        destinationConversationID: 1
        destinationDialogueID: 10
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 420.1407
        y: 506.9099
        width: 160
        height: 30
    - id: 10
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u50CF\u662F\u4E00\u9897\u653E\u4E86\u592A\u4E45\u7684\u7CD6\uFF0C\u751C\u5473\u65E9\u5C31\u6563\u4E86\uFF0C\u53EA\u5269\u4E0B\u4E00\u70B9\u784C\u4EBA\u7684\u5F62\u72B6\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 10
        destinationConversationID: 1
        destinationDialogueID: 11
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 420.1407
        y: 556.9099
        width: 160
        height: 30
    - id: 11
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u6709\u65F6\u5019\uFF0C\u5B83\u957F\u5F97\u50CF\u4E00\u4E2A\u4E16\u7EAA\uFF0C\u957F\u5230\u8DB3\u591F\u8BA9\u4E00\u5EA7\u57CE\u5E02\u6539\u5934\u6362\u9762\u2026\u2026"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 11
        destinationConversationID: 1
        destinationDialogueID: 12
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 420.1407
        y: 605.8461
        width: 160
        height: 30
    - id: 12
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u4F46\u6709\u65F6\u5019\u2026\u2026\u5B83\u53C8\u77ED\u5F97\u50CF\u6628\u5929\u7684\u4E8B\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 12
        destinationConversationID: 1
        destinationDialogueID: 13
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 420.1407
        y: 655.8461
        width: 160
        height: 30
    - id: 13
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u5F00\u573A\u767D\u4E0D\u9519\uFF0C\u6BD4\u4E0A\u6B21\u6A21\u62DF\u7684\u65F6\u5019\u81EA\u7136\u591A\u4E86\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 13
        destinationConversationID: 1
        destinationDialogueID: 14
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 420.1407
        y: 705.8461
        width: 160
        height: 30
    - id: 14
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u2026\u2026\u522B\u6253\u5C94\u3002\u6211\u5728\u627E\u611F\u89C9\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 14
        destinationConversationID: 1
        destinationDialogueID: 15
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 420.1407
        y: 755.8461
        width: 160
        height: 30
    - id: 15
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u627E\u4EC0\u4E48\u611F\u89C9\uFF1F\u4E94\u5E74\u524D\u7684\u611F\u89C9\u5417\uFF1F\u90A3\u53EF\u4E0D\u597D\u627E\uFF0C\u6574\u5EA7\u57CE\u90FD\u53D8\u4E86\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 15
        destinationConversationID: 1
        destinationDialogueID: 16
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 15
        destinationConversationID: 1
        destinationDialogueID: 17
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 15
        destinationConversationID: 1
        destinationDialogueID: 19
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 419.0769
        y: 804.7822
        width: 160
        height: 30
    - id: 16
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u25A0 \u4E0D\u2026\u2026\u662F\u91CD\u65B0\u5F00\u53E3\u7684\u611F\u89C9\u3002\u91CD\u65B0\u2026\u2026\u8FDE\u63A5\u7684\u611F\u89C9\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 16
        destinationConversationID: 1
        destinationDialogueID: 20
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 149.9279
        y: 900.2596
        width: 160
        height: 30
    - id: 17
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u25A0 \u8BF4\u5B9E\u8BDD\uFF0C\u6709\u70B9\u7D27\u5F20\u3002\u50CF\u7B2C\u4E00\u6B21\u4E0A\u64AD\u4E00\u6837\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 17
        destinationConversationID: 1
        destinationDialogueID: 22
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 430.779
        y: 901.9792
        width: 160
        height: 30
    - id: 19
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u25A0 \u6211\u5728\u627E\u4E00\u79CD\u2026\u2026\u719F\u6089\u611F\u3002\u60F3\u770B\u770B\u8FD9\u5EA7\u57CE\u5E02\u8FD8\u5269\u4E0B\u591A\u5C11\u6211\u4EEC\u8BA4\u8BC6\u7684\u6837\u5B50\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 19
        destinationConversationID: 1
        destinationDialogueID: 25
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 713.7577
        y: 899.4631
        width: 160
        height: 30
    - id: 20
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u90A3\u5C31\u597D\u597D\u611F\u53D7\u3002\u6BD5\u7ADF\u4E94\u5E74\u4E86\uFF0C\u5F88\u591A\u4EBA\u53EF\u80FD\u5DF2\u7ECF\u5FD8\u4E86\u8FD9\u4E2A\u9891\u9053\uFF0C\u5FD8\u4E86\u6211\u4EEC\u66FE\u7ECF\u5728\u8FD9\u91CC\u804A\u8FC7\u4EC0\u4E48\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 20
        destinationConversationID: 1
        destinationDialogueID: 21
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 150.9917
        y: 948.3992
        width: 160
        height: 30
    - id: 21
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u6240\u4EE5\uFF0C\u6211\u60F3\uFF0C\u6709\u5FC5\u8981\u91CD\u65B0\u8BF4\u4E00\u6B21\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 21
        destinationConversationID: 1
        destinationDialogueID: 28
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 150.9917
        y: 998.3992
        width: 160
        height: 30
    - id: 22
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u54FC\uFF0C\u4F60\u7B2C\u4E00\u6B21\u4E0A\u64AD\u7684\u65F6\u5019\uFF0C\u624B\u6296\u5F97\u8FDE\u8C03\u97F3\u53F0\u7684\u6309\u94AE\u90FD\u6309\u4E0D\u51C6\u3002\u4F60...\u8FD8\u8BB0\u5F97\u5417\uFF1F</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 22
        destinationConversationID: 1
        destinationDialogueID: 24
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 430.779
        y: 953.7183
        width: 160
        height: 30
    - id: 23
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u8C22\u8C22\u2026\u2026\u4F60\u8BF4\u5F97\u5BF9\uFF0C\u5C31\u5F53\u662F\u548C\u8001\u670B\u53CB\u804A\u5929\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 23
        destinationConversationID: 1
        destinationDialogueID: 28
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 431.8429
        y: 1060.101
        width: 160
        height: 30
    - id: 24
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u653E\u8F7B\u677E\uFF0C\u5C31\u5F53\u662F\u8DDF\u8001\u670B\u53CB\u804A\u5929\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 24
        destinationConversationID: 1
        destinationDialogueID: 23
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 430.779
        y: 1007.974
        width: 160
        height: 30
    - id: 25
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u522B\u627E\u4E86\uFF0C\u4F1A\u5931\u671B\u7684\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 25
        destinationConversationID: 1
        destinationDialogueID: 26
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 714.0215
        y: 949.4631
        width: 160
        height: 30
    - id: 26
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u2026\u2026\u662F\u554A\uFF0C\u90FD\u53D8\u4E86\u3002\u4F46\u58F0\u97F3\u603B\u8FD8\u80FD\u7559\u4E0B\u70B9\u4EC0\u4E48\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 26
        destinationConversationID: 1
        destinationDialogueID: 28
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 714.8215
        y: 999.4631
        width: 160
        height: 30
    - id: 28
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u8FD9\u4E2A\u8282\u76EE\uFF0C\u53EB\u300A\u6DF1\u591C\u8FDE\u7EBF\u300B\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 28
        destinationConversationID: 1
        destinationDialogueID: 29
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1206.909
        y: 49.33041
        width: 160
        height: 30
    - id: 29
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u5B83\u5F88\u7B80\u5355\u3002\u6CA1\u6709\u660E\u661F\u5609\u5BBE\uFF0C\u6CA1\u6709\u9884\u8BBE\u7684\u8BDD\u9898\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 29
        destinationConversationID: 1
        destinationDialogueID: 30
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1206.909
        y: 99.33041
        width: 160
        height: 30
    - id: 30
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u53EA\u6709\u6211\u548C\u4F60\uFF0C\u4E00\u6761\u5F00\u653E\u7684\u7EBF\u8DEF\uFF0C\u8FD8\u6709\u4E00\u4E2A\u2026\u2026\u53EF\u4EE5\u5B89\u653E\u6211\u4EEC\u65E0\u5904\u53EF\u8BF4\u7684\u5C0F\u6545\u4E8B\u7684\u5730\u65B9\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 30
        destinationConversationID: 1
        destinationDialogueID: 31
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1206.909
        y: 149.3305
        width: 160
        height: 30
    - id: 31
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u8BF4\u5F97\u5F88\u597D\u3002\u90A3\u4E48\u2026\u2026\u51FA\u53D1\u5427\u3002\u4ECA\u665A\uFF0C\u6211\u4EEC\u5148\u53BB\u770B\u770B\u90A3\u4E9B\u88AB\u9057\u5FD8\u7684\u89D2\u843D\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 31
        destinationConversationID: 1
        destinationDialogueID: 32
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1208.005
        y: 198.2345
        width: 160
        height: 30
    - id: 32
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u73B0\u5728\u5F00\u8F66\u7A7F\u884C\u5728\u8FD9\u5EA7\u57CE\u5E02\u91CC\u2026\u2026\u611F\u89C9\u65E2\u719F\u6089\uFF0C\u53C8\u964C\u751F\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 32
        destinationConversationID: 1
        destinationDialogueID: 33
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1206.909
        y: 249.3304
        width: 160
        height: 30
    - id: 33
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u9EC4\u91D1\u65F6\u4EE3\u7684\u9AA8\u67B6\u8FD8\u5728\uFF0C\u90A3\u4E9B\u6469\u5929\u5927\u697C\u7684\u526A\u5F71\uFF0C\u8FD8\u662F\u6211\u8BB0\u5FC6\u91CC\u7684\u6837\u5B50\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 33
        destinationConversationID: 1
        destinationDialogueID: 35
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1205.81
        y: 299.3304
        width: 160
        height: 30
    - id: 35
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u4F46\u8840\u8089\u5DF2\u7ECF\u6362\u4E86\u4E00\u526F\u6A21\u6837\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 35
        destinationConversationID: 1
        destinationDialogueID: 36
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1205.81
        y: 348.2313
        width: 160
        height: 30
    - id: 36
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u8840\u8089\uFF1F\u4F60\u8BF4\u7684\u662F\u90A3\u4E2A\u8001\u661F\u5149\u5267\u9662\u5417\uFF1F\u4E0A\u4E2A\u6708\u521A\u62C6\u5B8C\uFF0C\u73B0\u5728\u662F\u2026\u2026\u67A2\u7EBD\u901F\u8FD0\u7684\u4ED3\u5E93\u4E86\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 36
        destinationConversationID: 1
        destinationDialogueID: 37
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1205.81
        y: 398.2311
        width: 160
        height: 30
    - id: 37
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u2026\u2026\u662F\u554A\uFF0C\u8001\u661F\u5149\u5267\u9662\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 37
        destinationConversationID: 1
        destinationDialogueID: 38
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1205.81
        y: 448.2311
        width: 160
        height: 30
    - id: 38
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u73B0\u5728\u53D8\u6210\u4E86\u4E00\u4E2A\u5DE8\u5927\u7684\u3001\u6CA1\u6709\u7A97\u6237\u7684\u76D2\u5B50\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 38
        destinationConversationID: 1
        destinationDialogueID: 39
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1205.81
        y: 498.2311
        width: 160
        height: 30
    - id: 39
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u50CF\u4E00\u5EA7\u6C89\u9ED8\u7684\u3001\u62D2\u7EDD\u6C9F\u901A\u7684\u5821\u5792\u3002\u8FD8\u6709\u7A7A\u6C14\u91CC\u7684\u58F0\u97F3\u2026\u2026\u4E5F\u4E0D\u4E00\u6837\u4E86\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 39
        destinationConversationID: 1
        destinationDialogueID: 40
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1205.81
        y: 548.2311
        width: 160
        height: 30
    - id: 40
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u8FC7\u53BB\u90A3\u79CD\u65E0\u5904\u4E0D\u5728\u7684\u3001\u673A\u5668\u4F4E\u6C89\u7684\u55E1\u55E1\u58F0\uFF0C\u50CF\u57CE\u5E02\u5E73\u7A33\u7684\u5FC3\u8DF3\u2026\u2026\u73B0\u5728\uFF0C\u53EA\u5269\u4E0B\u98CE\u58F0\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 40
        destinationConversationID: 1
        destinationDialogueID: 41
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1205.81
        y: 596.6686
        width: 160
        height: 30
    - id: 41
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u548C\u66F4\u6DF1\u7684\u5BC2\u9759\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 41
        destinationConversationID: 1
        destinationDialogueID: 42
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1205.81
        y: 646.6686
        width: 160
        height: 30
    - id: 42
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u6211\u4EEC\u90FD\u5728\u90A3\u4E00\u665A\u5F04\u4E22\u4E86\u5F88\u591A\u4E1C\u897F\u3002\u786C\u76D8\u91CC\u7684\uFF0C\u4E91\u7AEF\u4E0A\u7684\u2026\u2026"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 42
        destinationConversationID: 1
        destinationDialogueID: 43
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1205.81
        y: 696.6686
        width: 160
        height: 30
    - id: 43
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u90A3\u4E9B\u6211\u4EEC\u66FE\u4EE5\u4E3A\u4F1A\u6C38\u8FDC\u5B58\u5728\u7684\u4E1C\u897F\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 43
        destinationConversationID: 1
        destinationDialogueID: 44
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1205.81
        y: 746.6686
        width: 160
        height: 30
    - id: 44
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u90A3\u4E2A\u65F6\u4EE3\u66FE\u5411\u6211\u4EEC\u4FDD\u8BC1\uFF0C\u4E00\u5207\u90FD\u4F1A\u88AB\u59A5\u5584\u4FDD\u7BA1\uFF0C\u76F4\u5230\u6C38\u8FDC\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 44
        destinationConversationID: 1
        destinationDialogueID: 45
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1205.81
        y: 795.1061
        width: 160
        height: 30
    - id: 45
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u4F46\u6C38\u8FDC\uFF0C\u597D\u50CF\u6BD4\u6211\u4EEC\u60F3\u8C61\u7684\u8981\u77ED\u4E00\u4E9B\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 45
        destinationConversationID: 1
        destinationDialogueID: 46
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1207.373
        y: 845.1061
        width: 160
        height: 30
    - id: 46
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u6C38\u6052\u2026\u2026\u672C\u6765\u5C31\u662F\u4E2A\u5962\u4F88\u54C1\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 46
        destinationConversationID: 1
        destinationDialogueID: 47
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1207.373
        y: 895.1061
        width: 160
        height: 30
    - id: 47
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u8FD9\u4F1A\u8BA9\u4F60\u5FCD\u4E0D\u4F4F\u6000\u7591\uFF0C\u4E0D\u662F\u5417\uFF1F"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 47
        destinationConversationID: 1
        destinationDialogueID: 48
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1207.373
        y: 945.1061
        width: 160
        height: 30
    - id: 48
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u5982\u679C\u4E00\u6BB5\u8BB0\u5FC6\u6CA1\u6709\u5907\u4EFD\uFF0C\u5982\u679C\u6CA1\u6709\u7B2C\u4E8C\u4E2A\u4EBA\u80FD\u4E3A\u4F60\u8BC1\u660E\u5B83\u7684\u5B58\u5728\u2026\u2026\u90A3\u5B83\u8FD8\u7B97\u771F\u5B9E\u5417\uFF1F"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 48
        destinationConversationID: 1
        destinationDialogueID: 49
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1207.373
        y: 993.5436
        width: 160
        height: 30
    - id: 49
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u8FD8\u662F\u8BF4\uFF0C\u6211\u4EEC\u6BCF\u4E2A\u4EBA\uFF0C\u90FD\u53EA\u662F\u6D3B\u5728\u81EA\u5DF1\u7684\u4E00\u573A\u5E7B\u89C9\u91CC\uFF1F"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 49
        destinationConversationID: 1
        destinationDialogueID: 50
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 49
        destinationConversationID: 1
        destinationDialogueID: 51
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 49
        destinationConversationID: 1
        destinationDialogueID: 52
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1207.373
        y: 1043.544
        width: 160
        height: 30
    - id: 50
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u25A0 \u6240\u4EE5\uFF0C\u4ECA\u665A\uFF0C\u6211\u60F3\u542C\u542C\u4F60\u4EEC\u7684\u5E7B\u89C9\uFF0C\u6216\u8005\u8BF4\u2026\u2026\u4F60\u4EEC\u7684\u771F\u5B9E\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 50
        destinationConversationID: 1
        destinationDialogueID: 53
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 902.6854
        y: 1152.919
        width: 160
        height: 30
    - id: 51
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u25A0 \u6211\u5076\u5C14\u4F1A\u60F3\u8D77\u4E00\u5BB6\u8857\u89D2\u7684\u4E66\u5E97\u2026\u2026\u4F46\u6211\u73B0\u5728\u5374\u600E\u4E48\u4E5F\u60F3\u4E0D\u8D77\u5B83\u7684\u540D\u5B57\u3002\u5B83\u771F\u7684\u5B58\u5728\u8FC7\u5417\uFF1F"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 51
        destinationConversationID: 1
        destinationDialogueID: 54
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1201.123
        y: 1156.044
        width: 160
        height: 30
    - id: 52
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u25A0 \u4E5F\u8BB8\uFF0C\u91CD\u8981\u7684\u4E0D\u662F\u8FC7\u53BB\uFF0C\u800C\u662F\u6211\u4EEC\u73B0\u5728\u9009\u62E9\u76F8\u4FE1\u4EC0\u4E48\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 52
        destinationConversationID: 1
        destinationDialogueID: 55
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1526.123
        y: 1160.731
        width: 160
        height: 30
    - id: 53
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u5582\uFF0C\u9646\u5927\u4E3B\u64AD\uFF0C\u4F60\u7684\u6DF1\u591C\u54F2\u5B66\u8BFE\u8BE5\u4E0B\u8BFE\u4E86\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 53
        destinationConversationID: 1
        destinationDialogueID: 56
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 902.6854
        y: 1202.919
        width: 160
        height: 30
    - id: 54
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u5582\uFF0C\u9646\u5927\u4E3B\u64AD\uFF0C\u522B\u5728\u8FD9\u4F24\u6625\u60B2\u79CB\u4E86\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 54
        destinationConversationID: 1
        destinationDialogueID: 56
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1201.123
        y: 1206.044
        width: 160
        height: 30
    - id: 55
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u5582\uFF0C\u9646\u5927\u4E3B\u64AD\uFF0C\u4F60\u7684\u5FC3\u7075\u9E21\u6C64\u8FD8\u662F\u7559\u7ED9\u542C\u4F17\u5427\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 55
        destinationConversationID: 1
        destinationDialogueID: 56
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1526.123
        y: 1210.731
        width: 160
        height: 30
    - id: 56
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u7559\u8A00\u677F\u4E0AID\u53EB\u591C\u822A\u8239\u7684\u542C\u4F17\u5DF2\u7ECF\u5237\u4E86\u4E09\u904D\u201C\u4E3B\u64AD\u8FD8\u5728\u5417\uFF1F\u201D\u4E86\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 56
        destinationConversationID: 1
        destinationDialogueID: 57
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2066.639
        y: 61.34479
        width: 160
        height: 30
    - id: 57
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u8FD8\u67094\u53F7\u53E3\u7684\u732B\u4E5F\u4E0A\u7EBF\u4E86\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 57
        destinationConversationID: 1
        destinationDialogueID: 58
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2066.639
        y: 81.90633
        width: 160
        height: 30
    - id: 58
      fields:
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "<color=white>\u4FE1\u53F7\u5F88\u7A33\u5B9A\uFF0C\u522B\u628A\u9505\u7529\u7ED9\u6211\u7684\u8BBE\u5907\u3002\u5F00\u59CB\u5DE5\u4F5C\u3002</color>"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 58
        destinationConversationID: 1
        destinationDialogueID: 59
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2066.639
        y: 131.9063
        width: 160
        height: 30
    - id: 59
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u62B1\u6B49\uFF0C\u8D70\u795E\u4E86\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 59
        destinationConversationID: 1
        destinationDialogueID: 60
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2066.639
        y: 181.9063
        width: 160
        height: 30
    - id: 60
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u6211\u7684\u2026\u2026\u5BFC\u64AD\uFF0C\u5728\u63D0\u9192\u6211\uFF0C\u6211\u4E0D\u662F\u5728\u81EA\u8A00\u81EA\u8BED\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 60
        destinationConversationID: 1
        destinationDialogueID: 61
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2066.639
        y: 231.9063
        width: 160
        height: 30
    - id: 61
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u8001\u6BDB\u75C5\u4E86\uFF0C\u5979\u8BF4\u7684\u5BF9\u3002\u6211\u5728\u8FD9\u91CC\uFF0C\u662F\u4E3A\u4E86\u4F60\u4EEC\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 61
        destinationConversationID: 1
        destinationDialogueID: 62
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2066.639
        y: 281.9063
        width: 160
        height: 30
    - id: 62
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u6211\u91CD\u65B0\u6253\u5F00\u8FD9\u4E2A\u9891\u9053\uFF0C\u4E0D\u4E3A\u4EC0\u4E48\u5B8F\u5927\u7684\u7406\u7531\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 62
        destinationConversationID: 1
        destinationDialogueID: 63
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2066.639
        y: 331.9063
        width: 160
        height: 30
    - id: 63
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u53EA\u662F\u60F3\u5728\u8FD9\u5EA7\u5DE8\u5927\u7684\u3001\u5145\u6EE1\u4E86\u9759\u7535\u566A\u97F3\u7684\u57CE\u5E02\u91CC\uFF0C\u4E3A\u5F7C\u6B64\u7559\u4E00\u4E2A\u53EF\u4EE5\u8BF4\u8BDD\u7684\u5730\u65B9\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 63
        destinationConversationID: 1
        destinationDialogueID: 64
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2068.364
        y: 381.9063
        width: 160
        height: 30
    - id: 64
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u4E00\u4E2A\u53EF\u4EE5\u5B89\u653E\u6211\u4EEC\u90A3\u4E9B\u65E0\u5904\u53EF\u8BF4\u7684\u5C0F\u6545\u4E8B\u3001\u5C0F\u60C5\u7EEA\u7684\u5730\u65B9\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 64
        destinationConversationID: 1
        destinationDialogueID: 66
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2066.639
        y: 431.9063
        width: 160
        height: 30
    - id: 66
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u4E5F\u8BB8\u662F\u5173\u4E8E\u4E00\u6BB5\u88AB\u9057\u5FD8\u7684\u56DE\u5FC6\uFF0C\u4E5F\u8BB8\u662F\u5173\u4E8E\u4E00\u4E2A\u4E0D\u6562\u8BF4\u51FA\u53E3\u7684\u68A6\u60F3\uFF0C\u53C8\u6216\u8005\uFF0C\u53EA\u662F\u4ECA\u665A\u6708\u8272\u5F88\u597D\uFF0C\u4F60\u60F3\u627E\u4E2A\u4EBA\u5206\u4EAB\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 66
        destinationConversationID: 1
        destinationDialogueID: 67
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2064.915
        y: 481.9063
        width: 160
        height: 30
    - id: 67
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u4E0D\u7BA1\u4F60\u60F3\u8BF4\u4EC0\u4E48\uFF0C\u4ECA\u665A\uFF0C\u6211\u90FD\u542C\u7740\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 67
        destinationConversationID: 1
        destinationDialogueID: 68
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2066.639
        y: 533.6304
        width: 160
        height: 30
    - id: 68
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u8FD9\u91CC\u662F\u300A\u6DF1\u591C\u8FDE\u7EBF\u300B\uFF0C\u6211\u662F\u9646\u4FEE\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 68
        destinationConversationID: 1
        destinationDialogueID: 69
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2070.088
        y: 583.6305
        width: 160
        height: 30
    - id: 69
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u5982\u679C\u4F60\u8FD8\u6CA1\u7761\uFF0C\u5982\u679C\u6709\u4E9B\u8BDD\u4E0D\u77E5\u9053\u8BE5\u5BF9\u8C01\u8BF4\u2026\u2026\u8FD9\u91CC\u7684\u4FE1\u53F7\uFF0C\u4E3A\u4F60\u655E\u5F00\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 69
        destinationConversationID: 1
        destinationDialogueID: 70
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2066.639
        y: 631.9063
        width: 160
        height: 30
    - id: 70
      fields:
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: "\u73B0\u5728\u662F\u2026\u2026\u51CC\u6668\u4E24\u70B9\u96F6\u4E03\u5206\u3002"
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2066.639
        y: 681.9063
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 666.656, y: 327.61633}
    canvasZoom: 0.28000045
  syncInfo:
    syncActors: 0
    syncItems: 0
    syncLocations: 0
    syncVariables: 0
    syncActorsDatabase: {fileID: 0}
    syncItemsDatabase: {fileID: 0}
    syncLocationsDatabase: {fileID: 0}
    syncVariablesDatabase: {fileID: 0}
  templateJson: 
